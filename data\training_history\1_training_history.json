[{"round_number": 1, "timestamp": "2025-08-08T22:57:42.607192", "data_samples": 20, "feature_files": {"fbcsp": "features\\1_round_1_fbcsp.pkl", "riemannian": "features\\1_round_1_riemannian.pkl", "tef": "features\\1_round_1_tef.pkl", "tangent_space": "features\\1_round_1_tangent_space.pkl", "plv": "features\\1_round_1_plv.pkl"}, "classifier_files": {"weighted_voting_system": "models\\weighted_voting_system_1_round_1.pkl", "performance_report": "models\\weighted_voting_report_1_round_1.txt"}, "performance_metrics": {"total_samples": 20.0, "motor_imagery_samples": 10.0, "rest_samples": 10.0, "data_quality_avg": 0.8000000000000002}, "training_config": {"trials_per_class": 10, "trial_duration": 2.0, "total_samples": 20}, "notes": "第1轮训练完成"}, {"round_number": 2, "timestamp": "2025-08-08T23:00:53.512301", "data_samples": 40, "feature_files": {"fbcsp": "features\\1_round_2_fbcsp.pkl", "riemannian": "features\\1_round_2_riemannian.pkl", "tef": "features\\1_round_2_tef.pkl", "tangent_space": "features\\1_round_2_tangent_space.pkl", "plv": "features\\1_round_2_plv.pkl"}, "classifier_files": {"weighted_voting_system": "models\\weighted_voting_system_1_round_2.pkl", "performance_report": "models\\weighted_voting_report_1_round_2.txt"}, "performance_metrics": {"total_samples": 40.0, "motor_imagery_samples": 20.0, "rest_samples": 20.0, "data_quality_avg": 0.8000000000000002}, "training_config": {"trials_per_class": 10, "trial_duration": 2.0, "total_samples": 40}, "notes": "第2轮训练完成"}]