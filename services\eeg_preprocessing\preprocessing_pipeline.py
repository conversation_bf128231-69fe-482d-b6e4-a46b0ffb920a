#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EEG预处理管道

实现传统滤波 + RLS滤波 + Kalman滤波的串联预处理流程
专门针对低信噪比脑电信号的实时处理

作者: AI Assistant
创建时间: 2025-01-02
"""

import numpy as np
import time
import logging
from typing import Optional, Dict, Any, Callable

from .preprocessing_config import PreprocessingConfig
from .traditional_filter import TraditionalFilterProcessor
from .rls_filter import RLSFilter
from .kalman_processor import KalmanFilterProcessor


class EEGPreprocessingPipeline:
    """
    EEG预处理管道

    实现三级串联预处理：
    1. 传统滤波（实时）：陷波 + 带通（逐包）；逐包标准化默认关闭
    2. RLS自适应滤波（实时）：快速自适应去噪
    3. Kalman滤波（实时）：状态估计和精细去噪

    所有处理均为实时模式，无批处理延迟，满足32ms处理要求。
    """
    
    def __init__(self, config: PreprocessingConfig):
        """
        初始化预处理管道

        Args:
            config: 预处理配置对象
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 根据配置初始化各级处理器
        self.traditional_processor = TraditionalFilterProcessor(config) if config.enable_traditional_filter else None
        self.rls_processor = RLSFilter(config) if config.enable_rls_filter else None
        self.kalman_processor = KalmanFilterProcessor(config) if config.enable_kalman_filter else None
        # 管道状态
        self.is_initialized = True
        self.total_packets_processed = 0

        # 性能统计（优化：使用固定大小deque防止内存泄漏）
        from collections import deque
        self.pipeline_stats = {
            'total_processing_time': deque(maxlen=1000),  # 保留最近1000次记录
            'traditional_time': deque(maxlen=1000),
            'rls_time': deque(maxlen=1000),
            'kalman_time': deque(maxlen=1000)
        }

        self.logger.info("EEG预处理管道初始化完成")
    
    def process_packet(self, packet_data: np.ndarray) -> Dict[str, Any]:
        """
        处理单个数据包
        
        Args:
            packet_data: 输入数据包 [8, 4] - 8通道，4个样本点
            
        Returns:
            dict: 包含各级处理结果
        """
        start_time = time.perf_counter()
        
        try:
            # 检查输入数据格式
            if packet_data.shape != (self.config.n_channels, 4):
                raise ValueError(f"输入数据形状错误: {packet_data.shape}, 期望: ({self.config.n_channels}, 4)")
            
            result = {
                'input_data': packet_data,  # 优化：直接引用，减少内存分配
                'traditional_output': None,
                'rls_output': None,
                'kalman_output': None,
                'processing_times': {},
                'pipeline_info': {
                    'packet_number': self.total_packets_processed + 1
                }
            }
            
            # 数据流处理：根据配置启用不同的处理级别
            current_data = packet_data

            # 第一级：传统滤波（如果启用）
            if self.config.enable_traditional_filter:
                traditional_start = time.perf_counter()
                traditional_result = self.traditional_processor.process(current_data)
                traditional_time = (time.perf_counter() - traditional_start) * 1000

                result['traditional_output'] = traditional_result
                result['processing_times']['traditional'] = traditional_time
                self.pipeline_stats['traditional_time'].append(traditional_time)
                current_data = traditional_result['filtered_data']
            else:
                result['traditional_output'] = None
                result['processing_times']['traditional'] = 0

            # 第二级：RLS自适应滤波（如果启用）
            if self.config.enable_rls_filter:
                rls_start = time.perf_counter()
                rls_filtered_data = self.rls_processor.process_packet(current_data)
                rls_time = (time.perf_counter() - rls_start) * 1000

                result['rls_output'] = {
                    'filtered_data': rls_filtered_data,
                    'filter_status': self.rls_processor.get_filter_status()
                }
                result['processing_times']['rls'] = rls_time
                self.pipeline_stats['rls_time'].append(rls_time)
                current_data = rls_filtered_data
            else:
                result['rls_output'] = None
                result['processing_times']['rls'] = 0

            # 第三级：Kalman滤波（如果启用）
            if self.config.enable_kalman_filter:
                kalman_start = time.perf_counter()
                kalman_result = self.kalman_processor.process(current_data)
                kalman_time = (time.perf_counter() - kalman_start) * 1000

                result['kalman_output'] = kalman_result
                result['processing_times']['kalman'] = kalman_time
                self.pipeline_stats['kalman_time'].append(kalman_time)
                current_data = kalman_result['filtered_data']
            else:
                result['kalman_output'] = None
                result['processing_times']['kalman'] = 0

            # 最终输出数据（此处保持实时逐包输出，不做窗口标准化；窗口级标准化在集成管理器中执行）
            result['final_output'] = current_data

            # 总处理时间
            total_time = (time.perf_counter() - start_time) * 1000
            result['processing_times']['total'] = total_time
            self.pipeline_stats['total_processing_time'].append(total_time)

            self.total_packets_processed += 1

            if self.config.enable_debug_output:
                self.logger.debug(f"管道处理完成 - 包#{self.total_packets_processed}, "
                                f"总耗时: {total_time:.3f}ms")

            return result
            
        except Exception as e:
            self.logger.error(f"预处理管道处理失败: {e}")
            return {
                'input_data': packet_data.copy(),
                'error': str(e),
                'processing_times': {'total': (time.perf_counter() - start_time) * 1000}
            }
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """获取管道性能统计"""
        stats = {
            'total_packets_processed': self.total_packets_processed,
            'average_times': {},
            'max_times': {},
            'min_times': {}
        }

        # 计算各级处理时间统计
        for stage in ['total_processing_time', 'traditional_time', 'rls_time', 'kalman_time']:
            times = self.pipeline_stats[stage]
            if times:
                stage_name = stage.replace('_time', '').replace('_processing', '')
                stats['average_times'][stage_name] = np.mean(times)
                stats['max_times'][stage_name] = np.max(times)
                stats['min_times'][stage_name] = np.min(times)
            else:
                stage_name = stage.replace('_time', '').replace('_processing', '')
                stats['average_times'][stage_name] = 0
                stats['max_times'][stage_name] = 0
                stats['min_times'][stage_name] = 0
        
        return stats
    
    def reset_stats(self):
        """重置管道统计"""
        self.pipeline_stats = {
            'total_processing_time': [],
            'traditional_time': [],
            'rls_time': [],
            'kalman_time': []
        }
        self.total_packets_processed = 0
        
        # 重置各处理器的统计
        if self.traditional_processor:
            self.traditional_processor.reset_stats()
        if self.rls_processor:
            self.rls_processor.reset()
        if self.kalman_processor:
            self.kalman_processor.reset_stats()

        self.logger.info("预处理管道统计已重置")

    def reset_pipeline(self):
        """重置整个管道"""
        self.reset_stats()

        # 重置各处理器状态
        if self.rls_processor:
            self.rls_processor.reset()
        if self.kalman_processor:
            self.kalman_processor.reset_filter()

        self.logger.info("预处理管道已重置")

    def get_processor_stats(self) -> Dict[str, Dict]:
        """获取各处理器的详细统计"""
        stats = {}
        if self.traditional_processor:
            stats['traditional'] = self.traditional_processor.get_performance_stats()
        if self.rls_processor:
            stats['rls'] = self.rls_processor.get_performance_stats()
        if self.kalman_processor:
            stats['kalman'] = self.kalman_processor.get_performance_stats()
        return stats

    def get_pipeline_status(self) -> Dict[str, Any]:
        """获取管道状态信息"""
        status = {
            'is_initialized': self.is_initialized,
            'total_packets_processed': self.total_packets_processed,
            'enabled_processors': self.config.get_enabled_steps()
        }
        if self.rls_processor:
            status['rls_filter_status'] = self.rls_processor.get_filter_status()
        else:
            status['rls_filter_status'] = None

        if self.kalman_processor:
            status['kalman_filter_status'] = self.kalman_processor.get_filter_status()
        else:
            status['kalman_filter_status'] = None

        return status

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {
            'total_packets_processed': self.total_packets_processed,
            'pipeline_stats': self.pipeline_stats.copy(),
            'processor_stats': self.get_processor_stats()
        }

        # 计算平均处理时间
        if self.pipeline_stats['total_processing_time']:
            stats['avg_total_time'] = np.mean(self.pipeline_stats['total_processing_time'])

        if self.pipeline_stats['traditional_time']:
            stats['avg_traditional_time'] = np.mean(self.pipeline_stats['traditional_time'])

        if self.pipeline_stats['rls_time']:
            stats['avg_rls_time'] = np.mean(self.pipeline_stats['rls_time'])

        if self.pipeline_stats['kalman_time']:
            stats['avg_kalman_time'] = np.mean(self.pipeline_stats['kalman_time'])

        return stats

    def stop(self):
        """停止预处理管道"""
        try:
            self.logger.info("预处理管道已停止")

        except Exception as e:
            self.logger.error(f"停止预处理管道失败: {e}")
