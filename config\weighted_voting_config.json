{"difficulty_level": 4, "custom_trigger_threshold": 0.7, "weight_method": "stacking", "use_simple_averaging": true, "performance_threshold": 0.4, "custom_weights": {}, "voting_weights": {}, "performance": {"fbcsp_svm": {"accuracy": 0.75, "cv_mean": 0.7333333333333334, "cv_std": 0.12247448713915891, "training_time": 0.0005044937133789062, "n_samples": 60}, "tef_rf": {"accuracy": 0.8833333333333333, "cv_mean": 0.65, "cv_std": 0.097182531580755, "training_time": 0.01754903793334961, "n_samples": 60}, "riemannian_meanfield": {"accuracy": 0.7666666666666667, "cv_mean": 0.5666666666666667, "cv_std": 0.062360956446232345, "training_time": 0.028924226760864258, "n_samples": 60}, "tangent_space_lr": {"accuracy": 0.9, "cv_mean": 0.65, "cv_std": 0.06236095644623234, "training_time": 0.0014581680297851562, "n_samples": 60}, "plv_svm": {"accuracy": 0.6666666666666666, "cv_mean": 0.6166666666666666, "cv_std": 0.17159383568311665, "training_time": 0.0009970664978027344, "n_samples": 60}}}