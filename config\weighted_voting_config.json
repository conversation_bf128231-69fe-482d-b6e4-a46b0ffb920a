{"difficulty_level": 3, "custom_trigger_threshold": 0.6, "weight_method": "stacking", "use_simple_averaging": true, "performance_threshold": 0.4, "custom_weights": {}, "voting_weights": {}, "performance": {"fbcsp_svm": {"accuracy": 0.7666666666666667, "cv_mean": 0.7166666666666667, "cv_std": 0.1632993161855452, "training_time": 0.001989603042602539, "n_samples": 60}, "tef_rf": {"accuracy": 0.85, "cv_mean": 0.4833333333333334, "cv_std": 0.13333333333333333, "training_time": 0.02013254165649414, "n_samples": 60}, "riemannian_meanfield": {"accuracy": 0.7833333333333333, "cv_mean": 0.65, "cv_std": 0.11055415967851333, "training_time": 0.0292055606842041, "n_samples": 60}, "tangent_space_lr": {"accuracy": 0.8333333333333334, "cv_mean": 0.6, "cv_std": 0.097182531580755, "training_time": 0.0020008087158203125, "n_samples": 60}, "plv_svm": {"accuracy": 0.7833333333333333, "cv_mean": 0.6333333333333334, "cv_std": 0.06666666666666665, "training_time": 0.0009975433349609375, "n_samples": 60}}}